#!/usr/bin/env python3

import re

# Test the parsing
sql_file = "u957990218_GpBKT.zayotech-com.20250727190356.sql"

with open(sql_file, 'r', encoding='utf-8') as f:
    content = f.read()

# Get the first post section
post_sections = re.findall(r"INSERT INTO `wp_posts` VALUES\s*(.*?)(?=INSERT INTO|/*!40000 ALTER TABLE|$)", content, re.DOTALL)

if post_sections:
    first_section = post_sections[0]
    print(f"First section length: {len(first_section)}")
    print(f"First 500 chars:")
    print(first_section[:500])
    print("\n" + "="*50 + "\n")
    
    # Try to find the first complete row
    # Look for the pattern (id,author,date,date,content,title,...)
    first_row_match = re.search(r'^\((.*?)\),', first_section, re.DOTALL)
    if first_row_match:
        first_row = first_row_match.group(1)
        print(f"First row content (first 500 chars):")
        print(first_row[:500])
        print("\n" + "="*50 + "\n")
        
        # Count commas to see how many fields we have
        comma_count = first_row.count(',')
        print(f"Comma count in first row: {comma_count}")
        
        # Try to split by comma (this will be wrong due to quoted content)
        simple_split = first_row.split(',')
        print(f"Simple comma split gives {len(simple_split)} parts")
        print(f"First 5 parts: {simple_split[:5]}")
