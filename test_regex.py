#!/usr/bin/env python3

import re

# Test the regex pattern
sql_file = "u957990218_GpBKT.zayotech-com.20250727190356.sql"

with open(sql_file, 'r', encoding='utf-8') as f:
    content = f.read()

# Test the regex pattern
post_sections = re.findall(r"INSERT INTO `wp_posts` VALUES\s*(.*?)(?=INSERT INTO|/*!40000 ALTER TABLE|$)", content, re.DOTALL)

print(f"Found {len(post_sections)} post sections")

if post_sections:
    print(f"First section length: {len(post_sections[0])}")
    print(f"First 200 chars: {post_sections[0][:200]}")
    print(f"Last 200 chars: {post_sections[0][-200:]}")
