import { StaticHeader } from '@/components/layout/StaticHeader';
import { StaticFooter } from '@/components/layout/StaticFooter';
import { StaticPostCard } from '@/components/blog/StaticPostCard';
import { blogPostService } from '@/lib/services/blogPostService';
import { databaseSettingsService } from '@/lib/services';

async function getPosts() {
  try {
    console.log('🔍 Fetching blog posts from D1...');
    const result = await blogPostService.getPosts({
      limit: 12
    });
    console.log('✅ Blog posts fetched:', result.posts.length);
    return result.posts;
  } catch (error) {
    console.error('❌ Error fetching blog posts:', error);
    // Return empty array - no fallback data
    return [];
  }
}

async function getSiteSettings() {
  try {
    const settings = await databaseSettingsService.getSiteSettings();
    return {
      ...settings,
      site_title: 'Zayotech - Shayari | Quotes | Wishes | Status',
      site_description: 'Zayotech पर पाएँ बेहतरीन <PERSON>, Quotes, Wishes और Status का शानदार कलेक्शन। प्यार, दर्द, दोस्ती और प्रेरणादायक शब्दों के साथ अपनी भावनाओं को खूबसूरती से बयां करें।',
      site_url: 'https://zayotech.com',
      default_language: 'hi' as const,
    };
  } catch (error) {
    console.error('Error fetching site settings:', error);
    return {
      site_title: 'Zayotech - Shayari | Quotes | Wishes | Status',
      site_description: 'Zayotech पर पाएँ बेहतरीन Shayari, Quotes, Wishes और Status का शानदार कलेक्शन। प्यार, दर्द, दोस्ती और प्रेरणादायक शब्दों के साथ अपनी भावनाओं को खूबसूरती से बयां करें।',
      site_url: 'https://zayotech.com',
      posts_per_page: 12,
      comment_moderation: true,
      default_language: 'hi' as const,
      theme: 'zayotech',
      timezone: 'Asia/Kolkata',
    };
  }
}

// No mock data - direct D1 connection only

export default async function Home() {
  const [posts, siteSettings] = await Promise.all([
    getPosts(),
    getSiteSettings()
  ]);

  return (
    <div className="min-h-screen bg-gray-50">
      <StaticHeader />

      <main className="container mx-auto px-4 py-8">
        {/* Hero Section */}
        <section className="text-center py-12 mb-12">
          <h1 className="text-4xl md:text-5xl font-bold text-gray-900 mb-4">
            {siteSettings.site_title}
          </h1>
          <p className="text-xl text-gray-600 max-w-2xl mx-auto">
            {siteSettings.site_description}
          </p>
        </section>

        {/* Blog Posts Grid */}
        {posts.length > 0 ? (
          <section className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 lg:gap-8">
            {posts.map((post) => (
              <StaticPostCard key={post.id} post={post} />
            ))}
          </section>
        ) : (
          <section className="text-center py-12">
            <h3 className="text-xl font-semibold text-gray-600 mb-4">
              कोई पोस्ट उपलब्ध नहीं है
            </h3>
            <p className="text-gray-500">
              डेटाबेस से पोस्ट लोड हो रहे हैं...
            </p>
          </section>
        )}
      </main>

      <StaticFooter />
    </div>
  );
}
